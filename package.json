{"name": "jd_h5st_server", "version": "0.0.1", "description": "京东h5st 接口版本", "author": "zhx47", "private": false, "license": "UNLICENSED", "url": "https://github.com/zhx47/jd_h5st_server", "bin": "./dist/index.js", "scripts": {"build": "nest build && ncc build dist/main.js -m", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "lint": "eslint \"{src,types}/**/*.ts\" --fix", "pkg:amd64": "yarn run build && pkg . -t node18-alpine-x64 -o ./dist/app", "pkg:arm64": "yarn run build && pkg . -t node18-alpine-arm64 -o ./dist/app"}, "dependencies": {"@keyv/redis": "^3.0.1", "@nestjs/common": "^10.4.3", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.4.3", "@nestjs/platform-express": "^10.4.3", "adler-32": "^1.3.1", "cache-manager": "^6.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "crypto-js": "^4.2.0", "express": "^4.21.1", "nest-winston": "^1.9.7", "nestjs-cache-manager-v6": "^2.2.6", "nestjs-cls": "^4.4.1", "qs": "^6.13.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "uuid": "^10.0.0", "winston": "^3.15.0"}, "devDependencies": {"@eslint/js": "^9.12.0", "@nestjs/cli": "^10.4.5", "@types/crypto-js": "^4.2.2", "@types/express": "^5.0.0", "@types/node": "^22.7.5", "@types/qs": "^6.9.16", "@types/uuid": "^10.0.0", "eslint": "^9.12.0", "eslint-config-prettier": "^9.1.0", "globals": "^15.11.0", "prettier": "^3.3.3", "typescript-eslint": "^8.8.1"}}